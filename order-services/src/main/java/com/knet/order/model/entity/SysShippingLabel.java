package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:09
 * @description: 物流运单模型
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_shipping_label", description = "物流运单模型")
@TableName("sys_shipping_label")
public class SysShippingLabel extends BaseEntity {

    @Schema(description = "批次编号")
    public String batchNo;

    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "电子面单地址")
    private String labelUrl;

    @Schema(description = "物流公司")
    private String expressCompany;

    @Schema(description = "总包裹数")
    private Integer packageCount;

    @Schema(description = "运费(单位：分)")
    private Long shippingFee;

    @Schema(description = "发货仓库")
    private String warehouse;


    /**
     * 创建物流运单
     *
     * @param shopLabelCenterVo kg 物流标签
     * @param warehouse         仓库
     * @return 物流运单
     */
    public static SysShippingLabel create(KnetShopLabelCenterVo shopLabelCenterVo, String warehouse) {
        return SysShippingLabel
                .builder()
                .batchNo(shopLabelCenterVo.batchNo)
                .trackingNumber(shopLabelCenterVo.trackingNo)
                .labelUrl(shopLabelCenterVo.labelFileUrl)
                .expressCompany("UPS")
                .packageCount(1)
                .shippingFee(shopLabelCenterVo.labelFee.longValue())
                .warehouse(warehouse)
                .build();
    }
}
