package com.knet.order.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:02
 * @description: kg 创建物流单请求体
 */

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetCreateShipment extends BaseRequest {

    @Schema(description = "仓库")
    private String warehouse;

    @Schema(description = "收货人")
    private ShipmentParty shipTo;

    @Schema(description = "包裹数")
    private Long count;

    @Data
    public static class ShipmentParty {
        private Integer addressId;
        private String name;
        private String attentionName;
        private String companyDisplayableName;
        private String taxIdentificationNumber;
        private Phone phone;
        private String shipperNumber;
        private String faxNumber;
        private String emailAddress;
        private Address address;

        @Data
        public static class Phone {
            private String number;
            private String extension;
        }

        @Data
        public static class Address {
            private List<String> addressLine;
            private String city;
            private String stateProvinceCode;
            private String postalCode;
            private String countryCode;
        }
    }

    public static KnetCreateShipment create(UserAddressDtoResp addressInfo, String warehouse) {
        KnetCreateShipment knetCreateShipment = new KnetCreateShipment();
        knetCreateShipment.setWarehouse(warehouse);
        knetCreateShipment.setShipTo(createShipmentParty(addressInfo));
        knetCreateShipment.setCount(1L);
        return knetCreateShipment;
    }

    private static ShipmentParty createShipmentParty(UserAddressDtoResp addressInfo) {
        ShipmentParty shipmentParty = new ShipmentParty();
        shipmentParty.setName(addressInfo.getFullName());
        ShipmentParty.Phone phone = new ShipmentParty.Phone();
        phone.setNumber(addressInfo.getPhoneNumber());
        shipmentParty.setPhone(phone);
        ShipmentParty.Address address = new ShipmentParty.Address();
        address.setAddressLine(List.of(addressInfo.getAddressLine1(), addressInfo.getAddressLine2()));
        address.setCity(addressInfo.getCity());
        //todo 省市编号
        address.setStateProvinceCode(addressInfo.getState());
        address.setPostalCode(addressInfo.getZipCode());
        address.setCountryCode("US");
        shipmentParty.setAddress(address);
        return shipmentParty;
    }
}
