package com.knet.order.model.dto.third.resp;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetCurrencyCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/16 13:58
 * @description: KG 创建物流标签返回体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetShopLabelCenterVo extends BaseResponse {
    @Schema(description = "id")
    public Integer id;

    @Schema(description = "批次编号")
    public String batchNo;

    @Schema(description = "物流单号")
    public String trackingNo;

    @Schema(description = "商家Id")
    public Integer shopId;

    @Schema(description = "商家UID")
    public String shopUid;

    @Schema(description = "商家名称")
    public String shopName;

    @Schema(description = "长")
    public String length;

    @Schema(description = "宽")
    public String width;

    @Schema(description = "高")
    public String height;

    @Schema(description = "长宽高单位")
    public String sizeUnit;

    @Schema(description = "重量1")
    public String weight1;

    @Schema(description = "重量1单位")
    public String weight1Unit;

    @Schema(description = "重量2")
    public String weight2;

    @Schema(description = "重量2单位")
    public String weight2Unit;

    @Schema(description = "费用")
    public BigDecimal labelFee;

    @Schema(description = "商家购入label价格")
    public BigDecimal shopLabelFee;

    @Schema(description = "ups最终收取的费用")
    public BigDecimal finalLabelFee;

    @Schema(description = "费用单位")
    public KnetCurrencyCode labelFeeUnit;

    @Schema(description = "发货地址信息ID")
    public Integer shipperAddressId;

    @Schema(description = "发货公司名称")
    public String shipperName;

    @Schema(description = "发货人名称")
    public String shipperCompanyName;

    @Schema(description = "发货人电话")
    public String shipperPhone;

    @Schema(description = "发货人地址")
    public String shipperAddress;

    @Schema(description = "发货人地址展示")
    public String shipperZipCode;
    @Schema(description = "发货人邮政编码")
    public String shipperProvince;
    @Schema(description = "发货人省份")
    public String shipperCity;
    @Schema(description = "发货人城市")
    public String shipperCountry;
    @Schema(description = "发货人国家")
    public String shipperDetail;
    @Schema(description = "发货人地址1")
    public String shipperDetail2;


    @Schema(description = "收货地址信息ID")
    public Integer recipientAddressId;

    @Schema(description = "收件公司名称")
    public String recipientCompanyName;

    @Schema(description = "收件人名称")
    public String recipientName;

    @Schema(description = "收件人电话")
    public String recipientPhone;

    @Schema(description = "收件人地址")
    public String recipientAddress;


    @Schema(description = "收件人地址展示")
    public String recipientZipCode;
    @Schema(description = "收件人邮政编码")
    public String recipientProvince;
    @Schema(description = "收件人省份")
    public String recipientCity;
    @Schema(description = "收件人城市")
    public String recipientCountry;
    @Schema(description = "收件人国家")
    public String recipientDetail;
    @Schema(description = "收件人地址1")
    public String recipientDetail2;

    @Schema(description = "当前状态")
    public String status;

    @Schema(description = "label服务")
    public String carrier;

    @Schema(description = "服务账号")
    public String carrierAccount;

    @Schema(description = " -1 已删除, 0 未删除")
    @TableLogic
    public Integer delFlag;

    @Schema(description = "创建时间")
    public Date gmtCreate;

    @Schema(description = "最近更新时间")
    public Date gmtModify;

    @Schema(description = "创建人")
    public Integer createById;

    @Schema(description = "标记文件路径")
    public String labelFileUrl;

    @Schema(description = "货物识别码")
    public String shipmentIdentificationNumber;

    @Schema(description = "1 不签名 2 签名 3 成年人签名")
    public String signature;

    @Schema(description = "货物识别码")
    public BigDecimal insuranceFee;

    @Schema(description = "label 参考备注")
    public String reference;

    @Schema(description = "商家签名花费")
    public BigDecimal shopSignatureFee;

    @Schema(description = "商家保价花费")
    public BigDecimal shopInsuranceFee;
}
