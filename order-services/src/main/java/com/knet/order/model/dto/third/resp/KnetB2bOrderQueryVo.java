package com.knet.order.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderItemStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:01
 * @description: b2b平台对外提供订单信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KnetB2bOrderQueryVo extends BaseResponse {
    /**
     * 父订单ID
     */
    @Schema(description = "父订单ID")
    private String parentOrderId;

    /**
     * 子订单ID
     */
    @Schema(description = "子订单ID")
    private Long itemId;

    /**
     * sku
     */
    @Schema(description = "sku")
    private String sku;

    /**
     * KG oneId
     */
    @Schema(description = "KG oneId")
    private String oneId;

    /**
     * b2b 商品唯一id
     */
    @Schema(description = "b2b 商品唯一id")
    private String listingId;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格", example = "100,美分")
    private int salePrice;


    /**
     * 到手价
     */
    @Schema(description = "到手价，美分")
    private int afterTakePrice;

    /**
     * 状态：
     *
     * @see com.knet.common.enums.KnetOrderItemStatus
     */
    @Schema(description = "状态")
    private KnetOrderItemStatus status;

    /**
     * 物流信息
     */
    @Schema(description = "物流信息")
    private KnetShippingLabelVo knetShippingLabel;

    /**
     * 售出时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单售出时间")
    private Date soldTime;

    /**
     * 取消时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单取消时间")
    private Date cancelTime;
}
