package com.knet.order.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 绑定Confirm和Return回调
 **/

@Configuration
public class RabbitConfig {

    @Bean
    public TopicExchange orderExchange() {
        // 订单业务交换机
        return new TopicExchange("order-exchange", true, false);
    }

    @Bean
    public Queue orderQueue() {
        // 订单队列（带死信配置）
        return QueueBuilder
                .durable("order-queue." + "order-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "order.*")
                .build();
    }

    @Bean
    public Binding orderBinding() {
        // 队列绑定
        return BindingBuilder
                .bind(orderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    @Bean
    public DirectExchange dlxExchange() {
        // 死信交换机（自动创建）
        return new DirectExchange("DLX", true, false);
    }

    @Bean
    public Queue dlxQueue() {
        // 死信队列
        return QueueBuilder
                .durable("dlx.order.queue")
                .build();
    }

    @Bean
    public Binding dlxBinding() {
        // 死信绑定
        return BindingBuilder
                .bind(dlxQueue())
                .to(dlxExchange())
                .with("order.*");
    }

    /**
     * 支付结果交换机
     */
    @Bean
    public TopicExchange paymentResultExchange() {
        return new TopicExchange("payment-result-exchange", true, false);
    }

    /**
     * 订单服务支付结果队列
     */
    @Bean
    public Queue paymentResultOrderQueue() {
        return QueueBuilder
                .durable("payment-result-queue.order-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "payment.result.*")
                .build();
    }

    /**
     * 订单服务支付结果队列绑定
     */
    @Bean
    public Binding paymentResultOrderBinding() {
        return BindingBuilder
                .bind(paymentResultOrderQueue())
                .to(paymentResultExchange())
                .with("payment.result");
    }

    /**
     * 库存补偿交换机
     */
    @Bean
    public TopicExchange inventoryCompensationExchange() {
        return new TopicExchange("inventory-compensation-exchange", true, false);
    }

    /**
     * 订单服务库存补偿队列
     */
    @Bean
    public Queue inventoryCompensationOrderQueue() {
        return QueueBuilder
                .durable("inventory-compensation-queue.order-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "inventory.failed.*")
                .build();
    }

    /**
     * 订单服务库存补偿队列绑定
     */
    @Bean
    public Binding inventoryCompensationOrderBinding() {
        return BindingBuilder
                .bind(inventoryCompensationOrderQueue())
                .to(inventoryCompensationExchange())
                .with("inventory.failed");
    }

    /**
     * 订单延迟交换机
     *
     * @return 订单延迟交换机
     */
    @Bean
    public CustomExchange delayedExchange() {
        Map<String, Object> args = new HashMap<>(2);
        // 延迟类型为direct路由
        args.put("x-delayed-type", "direct");
        return new CustomExchange(
                "order.delayed.exchange",
                "x-delayed-message",
                true,
                false,
                args
        );
    }

    /**
     * 订单超时队列
     */
    @Bean
    public Queue orderTimeoutQueue() {
        return new Queue("timeout.order.queue.order-services", true);
    }

    /**
     * 绑定队列到延迟交换机
     */
    @Bean
    public Binding bindingTimeoutQueue() {
        return BindingBuilder.bind(orderTimeoutQueue())
                .to(delayedExchange())
                .with("timeout.order")
                .noargs();
    }

    /**
     * 库存锁定成功交换机
     */
    @Bean
    public TopicExchange inventoryLockSuccessExchange() {
        return new TopicExchange("inventory-lock-success-exchange", true, false);
    }

    /**
     * 订单服务库存锁定成功队列
     */
    @Bean
    public Queue inventoryLockSuccessOrderQueue() {
        return QueueBuilder
                .durable("inventory-lock-success-queue.order-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "inventory.lock.success.*")
                .build();
    }

    /**
     * 订单服务库存锁定成功队列绑定
     */
    @Bean
    public Binding inventoryLockSuccessOrderBinding() {
        return BindingBuilder
                .bind(inventoryLockSuccessOrderQueue())
                .to(inventoryLockSuccessExchange())
                .with("inventory.lock.success");
    }
}
