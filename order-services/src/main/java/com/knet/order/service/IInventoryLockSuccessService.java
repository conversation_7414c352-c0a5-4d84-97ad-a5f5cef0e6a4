package com.knet.order.service;

import com.knet.common.dto.message.InventoryLockSuccessMessage;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:35
 * @description: 库存锁定成功处理服务接口
 */
public interface IInventoryLockSuccessService {

    /**
     * 处理库存锁定成功消息
     * 将锁定的商品信息（oneId、knetListingId,wareHouse）回写到订单项中
     *
     * @param lockSuccessMessage 库存锁定成功消息
     */
    void processInventoryLockSuccess(InventoryLockSuccessMessage lockSuccessMessage);
}
