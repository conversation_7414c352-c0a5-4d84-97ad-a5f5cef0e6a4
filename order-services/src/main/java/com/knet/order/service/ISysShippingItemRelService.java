package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.entity.SysShippingItemRel;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:50:02
 * @description: 针对表【SysShippingItemRel】的数据库操作Service
 */
public interface ISysShippingItemRelService extends IService<SysShippingItemRel> {
    /**
     * 保存物流单和商品的关联关系
     *
     * @param labelId 物流单ID
     * @param itemId  商品ID
     */
    void saveShippingItemRel(Long labelId, Long itemId);
}
