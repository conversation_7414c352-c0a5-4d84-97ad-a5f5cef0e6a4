package com.knet.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.order.mapper.SysShippingItemRelMapper;
import com.knet.order.model.entity.SysShippingItemRel;
import com.knet.order.service.ISysShippingItemRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:55:02
 * @description: 针对表【SysShippingItemRel】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysShippingItemRelServiceImpl extends ServiceImpl<SysShippingItemRelMapper, SysShippingItemRel> implements ISysShippingItemRelService {

    /**
     * 保存订单项与物流标签关联
     *
     * @param labelId 物流标签ID
     * @param itemId  订单项ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveShippingItemRel(Long labelId, Long itemId) {
        SysShippingItemRel shippingItemRel = SysShippingItemRel.builder()
                .labelId(labelId)
                .itemId(itemId)
                .build();
        this.save(shippingItemRel);
        log.info("保存订单项与物流标签关联: labelId={}, itemId={}", labelId, itemId);
    }
}
