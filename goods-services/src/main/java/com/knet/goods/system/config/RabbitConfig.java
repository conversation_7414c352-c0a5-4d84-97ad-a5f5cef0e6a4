package com.knet.goods.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 商品服务RabbitMQ配置
 **/

@Configuration
public class RabbitConfig {

    /**
     * 订单交换机（与order-service共享）
     */
    @Bean
    public TopicExchange orderExchange() {
        return new TopicExchange("order-exchange", true, false);
    }

    /**
     * 商品服务专用的订单队列
     */
    @Bean
    public Queue goodsOrderQueue() {
        return QueueBuilder
                .durable("order-queue.goods-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "order.*")
                .build();
    }

    /**
     * 商品服务订单队列绑定
     */
    @Bean
    public Binding goodsOrderBinding() {
        return BindingBuilder
                .bind(goodsOrderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange dlxExchange() {
        return new DirectExchange("DLX", true, false);
    }

    /**
     * 商品服务死信队列
     */
    @Bean
    public Queue goodsDlxQueue() {
        return QueueBuilder
                .durable("dlx.goods.order.queue")
                .build();
    }

    /**
     * 商品服务死信绑定
     */
    @Bean
    public Binding goodsDlxBinding() {
        return BindingBuilder
                .bind(goodsDlxQueue())
                .to(dlxExchange())
                .with("order.*");
    }

    /**
     * 库存补偿交换机
     */
    @Bean
    public TopicExchange inventoryCompensationExchange() {
        return new TopicExchange("inventory-compensation-exchange", true, false);
    }

    /**
     * 订单延迟交换机（与order-service共享）
     */
    @Bean
    public CustomExchange delayedExchange() {
        Map<String, Object> args = new HashMap<>(2);
        // 延迟类型为direct路由
        args.put("x-delayed-type", "direct");
        return new CustomExchange(
                "order.delayed.exchange",
                "x-delayed-message",
                true,
                false,
                args
        );
    }

    /**
     * 商品服务订单超时队列
     */
    @Bean
    public Queue goodsOrderTimeoutQueue() {
        return new Queue("timeout.order.queue.goods-services", true);
    }

    /**
     * 商品服务订单超时队列绑定
     */
    @Bean
    public Binding goodsOrderTimeoutBinding() {
        return BindingBuilder.bind(goodsOrderTimeoutQueue())
                .to(delayedExchange())
                .with("timeout.order")
                .noargs();
    }

    /**
     * 库存锁定成功交换机
     */
    @Bean
    public TopicExchange inventoryLockSuccessExchange() {
        return new TopicExchange("inventory-lock-success-exchange", true, false);
    }
}
