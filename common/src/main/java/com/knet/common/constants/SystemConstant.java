package com.knet.common.constants;

/**
 * <AUTHOR>
 * @date 2025/2/11
 * @description: 全局常量类
 */
public class SystemConstant {
    /**
     * header token
     */
    public static final String TOKEN = "token";
    /**
     * header X_API_KEY
     */
    public static final String X_API_KEY = "x-api-key";
    /**
     * api rate limit
     */
    public static final String API_RATE_LIMIT = "api:rate:limit:%s";
    /**
     * third api token
     */
    public static final String THIRD_API_TOKEN = "third:api:token:%s";
    /**
     * third api token expire
     */
    public static final Integer THIRD_API_TOKEN_EXPIRE = 7200;
    /**
     * 支付服务幂等性key前缀
     */
    public static final String PAYMENT_ORDER_PROCESSED = "payment:order:processed:%s";
    /**
     * 商品服务幂等性key前缀
     */
    public static final String GOODS_ORDER_PROCESSED = "goods:order:processed:%s";
    /**
     * 用户服务幂等性key前缀
     */
    public static final String USER_OPERATION_PROCESSED = "user:operation:processed:%s";
    /**
     * 订单服务处理支付结果消息幂等性key前缀
     */
    public static final String ORDER_PAYMENT_PROCESSED = "order:payment:processed:%s";
    /**
     * 支付服务处理库存失败消息幂等性key前缀
     */
    public static final String PAYMENT_INVENTORY_PROCESSED = "payment:inventory:processed:%s";
    /**
     * 订单服务处理库存失败消息幂等性key前缀
     */
    public static final String ORDER_INVENTORY_PROCESSED = "order:inventory:processed:%s";
    /**
     * 订单服务处理超时消息幂等性key前缀
     */
    public static final String ORDER_TIME_PROCESSED = "order:time:processed:%s";
    /**
     * 支付服务处理超时消息幂等性key前缀
     */
    public static final String PAYMENT_TIME_PROCESSED = "payment:time:processed:%s";
    /**
     * 商品服务处理超时消息幂等性key前缀
     */
    public static final String GOODS_TIME_PROCESSED = "goods:time:processed:%s";
    /**
     * 订单服务处理库存锁定成功消息幂等性key前缀
     */
    public static final String ORDER_INVENTORY_LOCK_SUCCESS_PROCESSED = "order:inventory:lock:success:processed:%s";
    /**
     * 消息已处理标识
     */
    public static final String PROCESSED = "PROCESSED";
}
